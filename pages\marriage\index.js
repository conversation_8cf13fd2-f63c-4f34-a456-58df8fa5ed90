const app = getApp()

Page({
  data: {
    // 男方信息
    male: {
      date: '',
      time: '',
      location: ''
    },
    // 女方信息
    female: {
      date: '',
      time: '',
      location: ''
    },
    // 分析结果
    analysis: null,
    // 是否显示结果
    showResult: false,
    // 加载状态
    loading: false,
    // 是否可以提交
    canSubmit: false,
    // 当前选择的分析类型
    currentType: 'overall',
    // 当前类型索引
    currentTypeIndex: 0,
    // 分析类型列表
    analysisTypes: [
      { id: 'overall', name: '总体评分' },
      { id: 'personality', name: '性格相合' },
      { id: 'career', name: '事业互助' },
      { id: 'wealth', name: '财运匹配' },
      { id: 'family', name: '家庭和谐' }
    ]
  },

  onLoad() {
    // 检查是否可以提交
    this.checkCanSubmit()
  },

  // 男方日期选择器
  bindMaleDateChange(e) {
    this.setData({
      'male.date': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 男方时间选择器
  bindMaleTimeChange(e) {
    this.setData({
      'male.time': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 男方地点选择
  chooseMaleLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          'male.location': res.address
        })
      }
    })
  },

  // 女方日期选择器
  bindFemaleDateChange(e) {
    this.setData({
      'female.date': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 女方时间选择器
  bindFemaleTimeChange(e) {
    this.setData({
      'female.time': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 女方地点选择
  chooseFemaleLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          'female.location': res.address
        })
      }
    })
  },

  // 切换分析类型
  switchAnalysisType(e) {
    const { type } = e.currentTarget.dataset
    const { analysisTypes } = this.data
    const currentTypeIndex = analysisTypes.findIndex(item => item.id === type)
    this.setData({
      currentType: type,
      currentTypeIndex
    })
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { male, female } = this.data
    const canSubmit = male.date && male.time && female.date && female.time
    this.setData({ canSubmit })
  },

  // 提交分析
  async submitAnalysis() {
    const { male, female, canSubmit } = this.data

    // 检查是否可以提交
    if (!canSubmit) {
      wx.showToast({
        title: '请填写必填信息',
        icon: 'none'
      })
      return
    }

    // 验证表单
    if (!male.date || !male.time || !female.date || !female.time) {
      wx.showToast({
        title: '请填写出生日期和时间',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    try {
      // 调用后端API
      const res = await wx.cloud.callFunction({
        name: 'getMarriageAnalysis',
        data: {
          male,
          female
        }
      })

      this.setData({
        analysis: res.result,
        showResult: true,
        loading: false
      })
    } catch (err) {
      console.error('合婚分析失败:', err)
      wx.showToast({
        title: '分析失败，请重试',
        icon: 'none'
      })
      this.setData({ loading: false })
    }
  },

  // 重新分析
  resetAnalysis() {
    this.setData({
      male: {
        date: '',
        time: '',
        location: ''
      },
      female: {
        date: '',
        time: '',
        location: ''
      },
      analysis: null,
      showResult: false,
      currentType: 'overall',
      currentTypeIndex: 0,
      canSubmit: false
    })
    this.checkCanSubmit()
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '八字合婚测算',
      path: '/pages/marriage/index'
    }
  }
}) 