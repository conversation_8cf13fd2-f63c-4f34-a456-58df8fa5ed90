# 恒琦易道小程序

一个功能完整的微信小程序，集成了玄学命理分析、AI智能问答等多项服务。

## 🚀 项目特色

### 核心功能
- 🔮 **AI智能问答** - 专业的玄学AI助手，支持命理分析
- 📊 **运势分析** - 八字、紫微斗数、五行分析等
- 💼 **社区功能** - 用户交流、经验分享等
- 🎨 **精美界面** - 现代化UI设计，流畅的动画效果

### 技术特色
- 📱 **自定义TabBar** - AI问答按钮凸出设计，带旋转动画
- 🎯 **组件化开发** - 完整的组件系统，易于维护
- ☁️ **云函数支持** - 后端API集成
- 🔧 **完整工具链** - 丰富的工具函数和配置

## 📁 项目结构

```
miniprogram-1/
├── 📱 pages/                 # 页面文件
│   ├── index/               # 首页
│   ├── ai-chat/             # AI问答页面 ⭐
│   ├── fortune/             # 运势分析
│   ├── profile/             # 个人中心
│   ├── bazi/                # 八字分析
│   ├── yijing/              # 易经卦象
│   ├── wuxing/              # 五行分析
│   └── ...                  # 其他页面
├── 🧩 components/           # 自定义组件
│   ├── loading/             # 加载组件
│   ├── toast/               # 提示组件
│   └── birth-info/          # 生辰信息组件
├── 🎨 custom-tab-bar/       # 自定义TabBar ⭐
├── 🔧 utils/                # 工具函数
│   ├── api.js               # API请求封装
│   ├── common.js            # 通用工具函数
│   ├── points.js            # 积分管理
│   └── ...                  # 其他工具
├── ☁️ cloudfunctions/       # 云函数
├── 🖼️ assets/               # 静态资源
└── ⚙️ config/               # 配置文件
```

## 🎨 UI特色

### TabBar设计
- **凸出式AI按钮** - 中间的AI问答按钮向上凸出25px
- **旋转动画** - AI图标持续顺时针旋转（4秒一圈）
- **浮动效果** - 按钮轻微上下浮动
- **阴影系统** - TabBar顶部渐变阴影，立体效果
- **主题色彩** - 紫色渐变主题 (#8a2be2 → #9932cc)

### 页面特色
- **现代化设计** - 毛玻璃效果、渐变背景
- **流畅动画** - 页面切换、加载动画
- **响应式布局** - 适配不同屏幕尺寸

## 🔧 技术架构

### 前端技术
- **小程序原生框架** - 微信小程序原生开发
- **组件化架构** - 可复用的组件系统
- **CSS3动画** - 硬件加速的流畅动画
- **响应式设计** - 多屏幕适配

### 后端支持
- **云函数** - 企业微信API集成
- **云存储** - 用户数据存储
- **API服务** - RESTful API设计

### 开发工具
- **微信开发者工具** - 主要开发环境
- **Node.js脚本** - 构建和检查工具
- **ESLint** - 代码规范检查

## 🚀 快速开始

### 环境要求
- 微信开发者工具 最新版
- Node.js 14+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd miniprogram-1
```

2. **安装依赖**
```bash
npm install
```

3. **配置小程序**
```javascript
// project.config.json
{
  "appid": "your-app-id",
  "projectname": "恒琦易道"
}
```

4. **开发者工具导入**
- 打开微信开发者工具
- 选择"导入项目"
- 选择项目目录
- 设置AppID

5. **项目检查**
```bash
node scripts/check-completeness.js
```

## 📱 功能模块

### 1. AI智能问答 🤖
- **智能对话** - 支持自然语言交互
- **命理分析** - 八字、运势、风水分析
- **快捷操作** - 预设常用问题
- **历史记录** - 聊天记录保存

**特色功能：**
- 打字机效果显示回答
- 实时输入状态提示
- 支持语音识别
- 个性化推荐

### 2. 运势分析 📊
- **八字排盘** - 专业八字分析
- **紫微斗数** - 详细命盘解读
- **五行分析** - 五行属性查询
- **流年运势** - 年度运势预测

**特色功能：**
- 可视化图表展示
- 详细文字解析
- 建议指导
- 分享功能

### 3. 社区功能 💼
- **用户交流** - 经验分享和讨论
- **内容发布** - 分享占卜心得
- **互动评论** - 用户互动交流
- **热门话题** - 热门内容推荐

### 4. 个人中心 👤
- **用户信息** - 个人资料管理
- **积分系统** - 签到、任务积分
- **历史记录** - 使用记录查看
- **设置中心** - 个性化配置

## 🎨 自定义TabBar详解

### 设计特点
```css
/* 凸出设计 */
.special-item {
  margin-top: -25px;
  z-index: 10;
}

/* 圆形背景 */
.special-icon-container {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #8a2be2, #9932cc);
  border-radius: 50%;
}

/* 旋转动画 */
.rotating {
  animation: rotate 4s linear infinite;
}
```

### 使用方法
```javascript
// 页面中同步TabBar状态
onShow() {
  if (typeof this.getTabBar === 'function' && this.getTabBar()) {
    this.getTabBar().setData({
      selected: 2 // AI问答页面索引
    })
  }
}
```

## 🔧 开发指南

### 添加新页面
1. 在`pages/`目录创建页面文件夹
2. 创建四个文件：`.js`, `.json`, `.wxml`, `.wxss`
3. 在`app.json`中注册页面路径

### 添加新组件
1. 在`components/`目录创建组件文件夹
2. 创建组件四个文件
3. 在使用页面的`.json`中引用组件

### 样式规范
- 使用rpx作为尺寸单位
- 遵循紫色主题色彩 (#8a2be2)
- 使用CSS3动画增强体验
- 保持响应式设计

### API规范
```javascript
// 统一使用api.js进行请求
const api = require('../../utils/api')

api.post('/user/info', { data })
  .then(res => {
    // 处理成功
  })
  .catch(err => {
    // 处理错误
  })
```

## 📋 部署清单

### 发布前检查
- [ ] 运行完整性检查脚本
- [ ] 测试所有页面功能
- [ ] 检查TabBar动画效果
- [ ] 验证企业微信API
- [ ] 测试云函数功能

### 生产环境配置
```javascript
// config/config.js
const config = {
  baseUrl: 'https://your-production-domain.com/api',
  env: {
    debug: false,
    version: '1.0.0'
  }
}
```

## 🐛 常见问题

### Q: TabBar不显示？
A: 检查`app.json`中是否设置了`"custom": true`

### Q: 组件找不到？
A: 确认组件路径和`.json`配置是否正确

### Q: 云函数调用失败？
A: 检查云函数部署状态和权限配置

### Q: 企业微信功能异常？
A: 验证企业微信配置和域名白名单

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 完成自定义TabBar设计
- 🚀 AI问答功能上线
- 💼 企业功能集成
- 🎨 UI界面优化
- 🔧 工具链完善

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目使用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目维护者：开发团队
- 邮箱：<EMAIL>
- 企业微信群：扫码加入

---

⭐ 如果这个项目对你有帮助，请给它一个Star！ 