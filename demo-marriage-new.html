<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>八字合婚 - 出生信息风格界面演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 0;
            margin: 0;
        }
        
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: transparent;
            min-height: 100vh;
        }
        
        /* 顶部标题区域 */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 30px 20px 20px;
            color: white;
        }
        
        .header-content {
            flex: 1;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 6px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }
        
        .subtitle {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
        }
        
        .header-decoration {
            margin-left: 10px;
        }
        
        .icon {
            font-size: 40px;
            opacity: 0.8;
        }
        
        /* 表单容器 */
        .form-container {
            background: #f8f9fa;
            border-radius: 20px 20px 0 0;
            padding: 20px;
            min-height: calc(100vh - 100px);
        }
        
        /* 信息卡片 */
        .info-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .card-icon {
            font-size: 20px;
            opacity: 0.7;
        }
        
        /* 表单项 */
        .form-item {
            margin-bottom: 16px;
        }
        
        .form-item:last-child {
            margin-bottom: 0;
        }
        
        .item-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .label {
            font-size: 16px;
            color: #333;
            font-weight: 500;
            margin-right: 4px;
        }
        
        .required {
            color: #ff4757;
            font-size: 14px;
            font-weight: bold;
        }
        
        .optional {
            color: #999;
            font-size: 12px;
            background: #f0f0f0;
            padding: 2px 4px;
            border-radius: 4px;
        }
        
        /* 选择器 */
        .picker-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 44px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 0 10px;
            background: #fafafa;
            cursor: pointer;
        }
        
        .picker-wrapper:hover {
            border-color: #667eea;
            background: white;
        }
        
        .picker-text {
            font-size: 16px;
            flex: 1;
        }
        
        .picker-text.placeholder {
            color: #bbb;
        }
        
        .picker-text.selected {
            color: #333;
        }
        
        .picker-icon {
            font-size: 16px;
            opacity: 0.5;
        }
        
        /* 提示卡片 */
        .tips-card {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 16px;
            border: 1px solid #f39c12;
        }
        
        .tips-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .tips-icon {
            font-size: 16px;
            margin-right: 6px;
        }
        
        .tips-title {
            font-size: 16px;
            font-weight: 600;
            color: #d68910;
        }
        
        .tips-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .tip-item {
            font-size: 13px;
            color: #b7950b;
            line-height: 1.4;
        }
        
        /* 提交按钮 */
        .submit-section {
            margin-top: 20px;
        }
        
        .submit-btn {
            width: 100%;
            height: 48px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: none;
            cursor: pointer;
        }
        
        .submit-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .submit-btn.disabled {
            background: #e8e8e8;
            color: #bbb;
            box-shadow: none;
            cursor: not-allowed;
        }
        
        .btn-text {
            font-size: 18px;
        }
        
        .btn-icon {
            font-size: 16px;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 表单区域 -->
        <div id="form-section">
            <!-- 顶部标题区域 -->
            <div class="header">
                <div class="header-content">
                    <div class="title">八字合婚</div>
                    <div class="subtitle">测算两人姻缘匹配度，探寻天作之合</div>
                </div>
                <div class="header-decoration">
                    <span class="icon">💕</span>
                </div>
            </div>

            <!-- 表单容器 -->
            <div class="form-container">
                <!-- 男方信息卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <span class="card-title">男方信息</span>
                        <span class="card-icon">👨</span>
                    </div>

                    <div class="form-item">
                        <div class="item-header">
                            <span class="label">出生日期</span>
                            <span class="required">*</span>
                        </div>
                        <div class="picker-wrapper">
                            <span class="picker-text placeholder">请选择出生日期</span>
                            <span class="picker-icon">📅</span>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="item-header">
                            <span class="label">出生时间</span>
                            <span class="required">*</span>
                        </div>
                        <div class="picker-wrapper">
                            <span class="picker-text placeholder">请选择出生时间</span>
                            <span class="picker-icon">⏰</span>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="item-header">
                            <span class="label">出生地点</span>
                            <span class="optional">选填</span>
                        </div>
                        <div class="picker-wrapper">
                            <span class="picker-text placeholder">点击选择出生地点</span>
                            <span class="picker-icon">📍</span>
                        </div>
                    </div>
                </div>

                <!-- 女方信息卡片 -->
                <div class="info-card">
                    <div class="card-header">
                        <span class="card-title">女方信息</span>
                        <span class="card-icon">👩</span>
                    </div>

                    <div class="form-item">
                        <div class="item-header">
                            <span class="label">出生日期</span>
                            <span class="required">*</span>
                        </div>
                        <div class="picker-wrapper">
                            <span class="picker-text placeholder">请选择出生日期</span>
                            <span class="picker-icon">📅</span>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="item-header">
                            <span class="label">出生时间</span>
                            <span class="required">*</span>
                        </div>
                        <div class="picker-wrapper">
                            <span class="picker-text placeholder">请选择出生时间</span>
                            <span class="picker-icon">⏰</span>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="item-header">
                            <span class="label">出生地点</span>
                            <span class="optional">选填</span>
                        </div>
                        <div class="picker-wrapper">
                            <span class="picker-text placeholder">点击选择出生地点</span>
                            <span class="picker-icon">📍</span>
                        </div>
                    </div>
                </div>

                <!-- 提示信息 -->
                <div class="tips-card">
                    <div class="tips-header">
                        <span class="tips-icon">💡</span>
                        <span class="tips-title">温馨提示</span>
                    </div>
                    <div class="tips-content">
                        <span class="tip-item">• 出生日期和时间为必填项</span>
                        <span class="tip-item">• 出生时间越精确，合婚结果越准确</span>
                        <span class="tip-item">• 您的信息将被安全保护，仅用于合婚分析</span>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="submit-section">
                    <button class="submit-btn disabled" onclick="showResult()">
                        <span class="btn-text">开始合婚测算</span>
                        <span class="btn-icon">💕</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showResult() {
            alert('演示页面 - 点击查看实际效果请在小程序中测试');
        }
    </script>
</body>
</html>
