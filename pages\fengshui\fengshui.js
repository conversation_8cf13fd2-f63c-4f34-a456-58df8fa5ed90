// pages/fengshui/fengshui.js
const app = getApp()
const globalState = require('../../utils/global-state')
const errorHandler = require('../../utils/error-handler')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 房屋信息
    directionIndex: null,
    directions: ['东', '南', '西', '北', '东南', '东北', '西南', '西北'],
    houseTypeIndex: null,
    houseTypes: ['住宅', '公寓', '别墅', '商铺', '办公室'],
    buildYear: '',
    
    // 户型信息
    rooms: 1,
    area: '',
    currentFloor: '',
    totalFloor: '',
    
    // 环境信息
    surroundings: [
      { label: '公园', value: 'park', icon: '🌳', checked: false },
      { label: '学校', value: 'school', icon: '🏫', checked: false },
      { label: '医院', value: 'hospital', icon: '🏥', checked: false },
      { label: '商场', value: 'mall', icon: '🏬', checked: false },
      { label: '地铁', value: 'subway', icon: '🚇', checked: false },
      { label: '河流', value: 'river', icon: '🌊', checked: false }
    ],
    specialLocations: [
      { label: '十字路口', value: 'crossroad', icon: '🚦', checked: false },
      { label: '墓地', value: 'cemetery', icon: '⚰️', checked: false },
      { label: '寺庙', value: 'temple', icon: '🏮', checked: false },
      { label: '电塔', value: 'tower', icon: '⚡', checked: false },
      { label: '垃圾站', value: 'garbage', icon: '🗑️', checked: false },
      { label: '变电站', value: 'power', icon: '⚡', checked: false }
    ],
    
    // 分析结果
    analysisResult: null,
    
    // 表单状态
    canSubmit: false,
    isLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 加载缓存数据
    this.loadCachedData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 加载缓存数据
    this.loadCachedData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { analysisResult } = this.data
    
    if (analysisResult) {
      return {
        title: `我的房屋风水分析结果：${analysisResult.overall}`,
        path: '/pages/fengshui/fengshui',
        imageUrl: '/assets/images/share-fengshui.jpg'
      }
    }
    
    return {
      title: '风水布局 - 专业的居家风水分析',
      path: '/pages/fengshui/fengshui',
      imageUrl: '/assets/images/share-fengshui.jpg'
    }
  },

  // 加载缓存数据
  loadCachedData() {
    try {
      const cachedData = wx.getStorageSync('fengshuiData')
      if (cachedData) {
        this.setData({
          directionIndex: cachedData.directionIndex,
          houseTypeIndex: cachedData.houseTypeIndex,
          buildYear: cachedData.buildYear,
          rooms: cachedData.rooms || 1,
          area: cachedData.area,
          currentFloor: cachedData.currentFloor,
          totalFloor: cachedData.totalFloor,
          surroundings: cachedData.surroundings || this.data.surroundings,
          specialLocations: cachedData.specialLocations || this.data.specialLocations
        })
        this.checkCanSubmit()
      }
    } catch (error) {
      console.error('加载缓存数据失败:', error)
    }
  },

  // 保存数据到缓存
  saveCachedData() {
    try {
      const {
        directionIndex,
        houseTypeIndex,
        buildYear,
        rooms,
        area,
        currentFloor,
        totalFloor,
        surroundings,
        specialLocations
      } = this.data
      
      wx.setStorageSync('fengshuiData', {
        directionIndex,
        houseTypeIndex,
        buildYear,
        rooms,
        area,
        currentFloor,
        totalFloor,
        surroundings,
        specialLocations,
        timestamp: new Date().getTime()
      })
    } catch (error) {
      console.error('保存缓存数据失败:', error)
    }
  },

  // 房屋朝向选择
  onDirectionChange(e) {
    this.setData({
      directionIndex: parseInt(e.detail.value)
    })
    this.checkCanSubmit()
  },

  // 房屋类型选择
  onHouseTypeChange(e) {
    this.setData({
      houseTypeIndex: parseInt(e.detail.value)
    })
    this.checkCanSubmit()
  },

  // 建筑年份选择
  onBuildYearChange(e) {
    this.setData({
      buildYear: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 房间数量增减
  increaseRooms() {
    this.setData({
      rooms: this.data.rooms + 1
    })
    this.checkCanSubmit()
  },

  decreaseRooms() {
    if (this.data.rooms > 1) {
      this.setData({
        rooms: this.data.rooms - 1
      })
      this.checkCanSubmit()
    }
  },

  // 面积输入
  onAreaInput(e) {
    this.setData({
      area: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 楼层输入
  onCurrentFloorInput(e) {
    this.setData({
      currentFloor: e.detail.value
    })
    this.checkCanSubmit()
  },

  onTotalFloorInput(e) {
        this.setData({
      totalFloor: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 周边环境选择
  toggleSurrounding(e) {
    const index = e.currentTarget.dataset.index
    const surroundings = this.data.surroundings
    surroundings[index].checked = !surroundings[index].checked
    
    this.setData({ surroundings })
    this.checkCanSubmit()
  },

  // 特殊位置选择
  toggleSpecialLocation(e) {
    const index = e.currentTarget.dataset.index
    const specialLocations = this.data.specialLocations
    specialLocations[index].checked = !specialLocations[index].checked
    
    this.setData({ specialLocations })
    this.checkCanSubmit()
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const {
      directionIndex,
      houseTypeIndex,
      buildYear,
      area,
      currentFloor,
      totalFloor
    } = this.data
    
    const canSubmit = 
      directionIndex !== null &&
      houseTypeIndex !== null &&
      buildYear &&
      area &&
      currentFloor &&
      totalFloor &&
      !isNaN(currentFloor) &&
      !isNaN(totalFloor) &&
      parseInt(currentFloor) <= parseInt(totalFloor)

    this.setData({ canSubmit })
    
    // 如果数据有更新，保存到缓存
    if (canSubmit) {
      this.saveCachedData()
    }
  },

  // 开始分析
  async analyzeFengshui() {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      })
      return
    }

    this.setData({ isLoading: true })

    try {
      // 调用云函数进行风水分析
      const result = await wx.cloud.callFunction({
        name: 'analyzeFengshui',
        data: {
          direction: this.data.directions[this.data.directionIndex],
          houseType: this.data.houseTypes[this.data.houseTypeIndex],
          buildYear: this.data.buildYear,
          rooms: this.data.rooms,
          area: parseFloat(this.data.area),
          floor: {
            current: parseInt(this.data.currentFloor),
            total: parseInt(this.data.totalFloor)
          },
          surroundings: this.data.surroundings
            .filter(item => item.checked)
            .map(item => item.value),
          specialLocations: this.data.specialLocations
            .filter(item => item.checked)
            .map(item => item.value)
        }
      })

      if (result.result.success) {
        this.setData({
          analysisResult: {
            totalScore: result.result.data.totalScore,
            overall: result.result.data.overall,
            description: result.result.data.description,
            details: [
              {
                type: 'direction',
                title: '方位吉凶',
                icon: '🧭',
                score: result.result.data.directionScore,
                level: this.getScoreLevel(result.result.data.directionScore),
                content: result.result.data.directionAnalysis,
                color: this.getScoreColor(result.result.data.directionScore)
              },
              {
                type: 'layout',
                title: '格局布局',
                icon: '📐',
                score: result.result.data.layoutScore,
                level: this.getScoreLevel(result.result.data.layoutScore),
                content: result.result.data.layoutAnalysis,
                color: this.getScoreColor(result.result.data.layoutScore)
              },
              {
                type: 'environment',
                title: '环境气场',
                icon: '🌳',
                score: result.result.data.environmentScore,
                level: this.getScoreLevel(result.result.data.environmentScore),
                content: result.result.data.environmentAnalysis,
                color: this.getScoreColor(result.result.data.environmentScore)
              }
            ],
            suggestions: result.result.data.suggestions
          }
        })
      } else {
        throw new Error(result.result.message || '分析失败')
      }
    } catch (error) {
      console.error('风水分析失败:', error)
      wx.showToast({
        title: error.message || '分析失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  // 获取分数等级
  getScoreLevel(score) {
    if (score >= 80) return 'high'
    if (score >= 60) return 'medium'
    return 'low'
  },

  // 获取分数对应的颜色
  getScoreColor(score) {
    if (score >= 80) return '#a8e6cf'
    if (score >= 60) return '#ffd3b6'
    return '#ffaaa5'
  },

  // 重置分析
  resetAnalysis() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有信息重新分析吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            directionIndex: null,
            houseTypeIndex: null,
            buildYear: '',
            rooms: 1,
            area: '',
            currentFloor: '',
            totalFloor: '',
            surroundings: this.data.surroundings.map(item => ({...item, checked: false})),
            specialLocations: this.data.specialLocations.map(item => ({...item, checked: false})),
            analysisResult: null,
            canSubmit: false
          })
          // 清除缓存
          wx.removeStorageSync('fengshuiData')
        }
      }
    })
  },
})