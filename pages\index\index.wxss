/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color) !important;
}

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f0ff 0%, #e8dff5 100%);
  padding-bottom: env(safe-area-inset-bottom);
}

.scrollarea {
  height: 100vh;
  overflow: hidden;
  background: transparent;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--text-light);
  width: 80%;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
  border: 4rpx solid var(--primary-lightest);
  box-shadow: 0 8rpx 24rpx var(--shadow-color);
}

.usermotto {
  margin-top: 200px;
}

/* 容器美化 */
.container {
  min-height: 100vh;
  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);
}

/* 错误提示样式 - 美化 */
.error-tip {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx;
}

/* 加载中 - 美化 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 主要内容 */
.content {
  padding: 20rpx;
}

/* 时间问候头部 - 增强样式 */
.time-greeting-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20rpx;
  border-radius: 20rpx;
  margin: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.greeting-section {
  margin-bottom: 20rpx;
}

.greeting-text {
  font-size: 36rpx;
  color: #333;
  margin-right: 10rpx;
}

.user-name {
  font-size: 32rpx;
  color: #666;
}

/* 日历部分新样式 */
.calendar-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.solar-date {
  display: flex;
  align-items: center;
}

.date-number {
  font-size: 72rpx;
  font-weight: bold;
  color: #333;
  margin-right: 20rpx;
}

.date-info {
  display: flex;
  flex-direction: column;
}

.month-year {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.weekday {
  font-size: 24rpx;
  color: #999;
}

.lunar-info {
  flex: 1;
  margin-left: 30rpx;
}

.lunar-date {
  margin-bottom: 10rpx;
}

.lunar-text {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.ganzhi-year {
  font-size: 24rpx;
  color: #999;
}

.solar-term {
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
}

.term-name {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.term-desc {
  font-size: 24rpx;
  color: #999;
}

.yiji-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.yi-content, .ji-content {
  display: flex;
  margin-bottom: 16rpx;
}

.yi-content:last-child {
  margin-bottom: 0;
}

.yi-title, .ji-title {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.yi-title {
  background: #e8f5e9;
  color: #4caf50;
}

.ji-title {
  background: #ffebee;
  color: #f44336;
}

.yi-items, .ji-items {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.yi-item, .ji-item {
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.yi-item {
  background: #e8f5e9;
  color: #4caf50;
}

.ji-item {
  background: #ffebee;
  color: #f44336;
}

/* 轮播图 - 美化 */
.banner {
  height: 350rpx;
  margin: 20rpx 0;
  border-radius: 25rpx;
  overflow: hidden;
}

.banner-item {
  position: relative;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 25rpx;
}

.banner-title {
  position: absolute;
  bottom: 30rpx;
  left: 30rpx;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}

/* 轮播图圆点优化 */
.wx-swiper-dots {
  bottom: 20rpx !important;
}

.wx-swiper-dot {
  width: 16rpx;
  height: 16rpx;
  margin: 0 8rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.wx-swiper-dot-active {
  width: 32rpx;
  background: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 测算类目 - 美化 */
.category-section {
  margin: 40rpx 0;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding: 0 10rpx;
}

.section-more {
  font-size: 26rpx;
  color: #9575cd;
  font-weight: normal;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25rpx;
  padding: 0 10rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 15rpx;
  background: white;
  border-radius: 25rpx;
  box-shadow: 0 6rpx 20rpx rgba(149, 117, 205, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.category-item::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(149, 117, 205, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.category-item:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(149, 117, 205, 0.2);
}

.category-item:active::before {
  opacity: 1;
  animation: shimmer 0.6s ease;
}

@keyframes shimmer {
  0% { transform: rotate(45deg) translateX(-100%); }
  100% { transform: rotate(45deg) translateX(100%); }
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.category-name {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  font-weight: 500;
}

/* 热门测算 */
.hot-section {
  margin-top: 50rpx;
}

.hot-list {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.hot-item {
  display: flex;
  background: white;
  border-radius: 25rpx;
  padding: 25rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.hot-item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, rgba(149, 117, 205, 0.1), transparent);
  border-radius: 0 0 0 100%;
}

.hot-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
}

.hot-image {
  width: 180rpx;
  height: 140rpx;
  border-radius: 20rpx;
  margin-right: 25rpx;
  object-fit: cover;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.hot-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.hot-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  line-height: 1.4;
  background: linear-gradient(135deg, #333 0%, #666 100%);
  -webkit-background-clip: text;
  background-clip: text;
}

.hot-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 15rpx;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.hot-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.hot-price {
  font-size: 32rpx;
  color: #ff6b6b;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(255, 107, 107, 0.2);
}

.hot-count {
  font-size: 24rpx;
  color: #999;
  background: #f5f0ff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

/* 热门文章 */
.article-section {
  margin-top: 50rpx;
  padding-bottom: 40rpx;
}

.article-list {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.article-item {
  display: flex;
  background: white;
  border-radius: 25rpx;
  padding: 25rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.article-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #9575cd 0%, #b39ddb 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.article-item:active::after {
  transform: scaleX(1);
}

.article-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.12);
}

.article-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 20rpx;
  margin-right: 25rpx;
  object-fit: cover;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.article-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.article-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 15rpx;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  transition: color 0.3s ease;
}

.article-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.article-author {
  display: flex;
  align-items: center;
  padding-left: 25rpx;
  position: relative;
}

.article-author::before {
  content: '✍';
  position: absolute;
  left: 0;
  font-size: 20rpx;
}

.article-views {
  background: linear-gradient(135deg, #f5f0ff 0%, #ece7f5 100%);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

/* 节气提醒和签到区域 - 美化 */
.header-section {
  margin-bottom: 30rpx;
}

.solar-terms-notice {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.1);
}

.notice-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.notice-text {
  font-size: 28rpx;
  color: #e65100;
  font-weight: 500;
}

.sign-in-section {
  margin-bottom: 32rpx;
}

.sign-in-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
  padding: 30rpx;
  border-radius: 25rpx;
  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.15);
}

.sign-in-icon {
  font-size: 60rpx;
  margin-right: 25rpx;
}

.sign-in-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.sign-title {
  font-size: 32rpx;
  color: #2e7d32;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.sign-desc {
  font-size: 24rpx;
  color: #558b2f;
}

.sign-btn {
  background: #4caf50;
  color: white;
  padding: 15rpx 35rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

/* 最近使用功能 - 美化 */
.section {
  margin-bottom: 40rpx;
}

.recent-scroll {
  white-space: nowrap;
  margin-top: 20rpx;
}

.recent-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  width: 140rpx;
  padding: 20rpx;
  margin-right: 20rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.recent-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.recent-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 增强现有样式 */
.grid-item,
.service-item {
  position: relative;
  overflow: hidden;
  background: var(--card-background);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 20rpx var(--shadow-color);
  border: 1rpx solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.grid-item::before,
.service-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.grid-item:active,
.service-item:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 3rpx 10rpx var(--shadow-color);
}

.grid-item:active::before,
.service-item:active::before {
  opacity: 0.08;
}

/* 骨架屏加载样式 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-banner {
  height: 300rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.skeleton-card {
  height: 120rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}

.skeleton-text {
  height: 32rpx;
  border-radius: 4rpx;
  margin-bottom: 16rpx;
}

.skeleton-text.short {
  width: 60%;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .grid-container {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .grid-item {
    padding: 20rpx;
  }
  
  .grid-icon {
    width: 48rpx;
    height: 48rpx;
  }
}