// marriage.js
const app = getApp()
const globalState = require('../../utils/global-state')
const errorHandler = require('../../utils/error-handler')
const { calculateBazi } = require('../../utils/bazi')

Page({
  data: {
    // 男方信息
    maleInfo: {
      name: '',
      birthDate: '',
      birthTime: ''
    },
    // 女方信息
    femaleInfo: {
      name: '',
      birthDate: '',
      birthTime: ''
    },
    // 分析结果
    analysisResult: null,
    // 是否可以提交
    canSubmit: false,
    // 加载状态
    isLoading: false
  },

  onLoad(options) {
    // 如果有缓存的数据，加载缓存数据
    this.loadCachedData()
  },

  // 加载缓存数据
  loadCachedData() {
    try {
      const cachedData = wx.getStorageSync('marriageAnalysisData')
      if (cachedData) {
        this.setData({
          maleInfo: cachedData.maleInfo || this.data.maleInfo,
          femaleInfo: cachedData.femaleInfo || this.data.femaleInfo
        })
        this.checkCanSubmit()
      }
    } catch (error) {
      console.error('加载缓存数据失败:', error)
    }
  },

  // 保存数据到缓存
  saveCachedData() {
    try {
      const { maleInfo, femaleInfo } = this.data
      wx.setStorageSync('marriageAnalysisData', {
        maleInfo,
        femaleInfo,
        timestamp: new Date().getTime()
      })
    } catch (error) {
      console.error('保存缓存数据失败:', error)
    }
  },

  // 男方姓名输入
  onMaleNameInput(e) {
    this.setData({
      'maleInfo.name': e.detail.value.trim()
    })
    this.checkCanSubmit()
  },

  // 男方日期选择
  onMaleDateChange(e) {
    this.setData({
      'maleInfo.birthDate': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 男方时间选择
  onMaleTimeChange(e) {
    this.setData({
      'maleInfo.birthTime': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 女方姓名输入
  onFemaleNameInput(e) {
    this.setData({
      'femaleInfo.name': e.detail.value.trim()
    })
    this.checkCanSubmit()
  },

  // 女方日期选择
  onFemaleDateChange(e) {
    this.setData({
      'femaleInfo.birthDate': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 女方时间选择
  onFemaleTimeChange(e) {
    this.setData({
      'femaleInfo.birthTime': e.detail.value
    })
    this.checkCanSubmit()
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { maleInfo, femaleInfo } = this.data
    const canSubmit = 
      maleInfo.name && 
      maleInfo.birthDate && 
      maleInfo.birthTime &&
      femaleInfo.name && 
      femaleInfo.birthDate && 
      femaleInfo.birthTime

    this.setData({ canSubmit })
    
    // 如果数据有更新，保存到缓存
    if (canSubmit) {
      this.saveCachedData()
    }
  },

  // 开始分析
  async startAnalysis() {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善必填信息',
        icon: 'none'
      })
      return
    }

    this.setData({ isLoading: true })

    try {
      // 调用云函数进行八字合婚分析
      const { maleInfo, femaleInfo } = this.data
      
      // 计算男女双方八字
      const maleBazi = calculateBazi(maleInfo.birthDate, maleInfo.birthTime)
      const femaleBazi = calculateBazi(femaleInfo.birthDate, femaleInfo.birthTime)

      const result = await wx.cloud.callFunction({
        name: 'analyzeMarriage',
        data: {
          male: {
            ...maleInfo,
            bazi: maleBazi
          },
          female: {
            ...femaleInfo,
            bazi: femaleBazi
          }
        }
      })

      // 处理分析结果
      if (result.result.success) {
        this.setData({
          analysisResult: {
            totalScore: result.result.data.totalScore,
            compatibility: result.result.data.compatibility,
            description: result.result.data.description,
            maleBazi: maleBazi,
            femaleBazi: femaleBazi,
            details: [
              {
                type: 'personality',
                title: '性格相合',
                icon: '🤝',
                score: result.result.data.personalityScore,
                level: this.getScoreLevel(result.result.data.personalityScore),
                content: result.result.data.personalityAnalysis,
                color: this.getScoreColor(result.result.data.personalityScore)
              },
              {
                type: 'fortune',
                title: '福运契合',
                icon: '🎯',
                score: result.result.data.fortuneScore,
                level: this.getScoreLevel(result.result.data.fortuneScore),
                content: result.result.data.fortuneAnalysis,
                color: this.getScoreColor(result.result.data.fortuneScore)
              },
              {
                type: 'relationship',
                title: '感情基础',
                icon: '💑',
                score: result.result.data.relationshipScore,
                level: this.getScoreLevel(result.result.data.relationshipScore),
                content: result.result.data.relationshipAnalysis,
                color: this.getScoreColor(result.result.data.relationshipScore)
              }
            ],
            suggestions: result.result.data.suggestions
          }
        })
      } else {
        throw new Error(result.result.message || '分析失败')
      }
    } catch (error) {
      console.error('合婚分析失败:', error)
      wx.showToast({
        title: error.message || '分析失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ isLoading: false })
    }
  },

  // 获取分数等级
  getScoreLevel(score) {
    if (score >= 80) return 'high'
    if (score >= 60) return 'medium'
    return 'low'
  },

  // 获取分数对应的颜色
  getScoreColor(score) {
    if (score >= 80) return '#a8e6cf'
    if (score >= 60) return '#ffd3b6'
    return '#ffaaa5'
  },

  // 重置分析
  resetAnalysis() {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有信息重新测算吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            maleInfo: {
              name: '',
              birthDate: '',
              birthTime: ''
            },
            femaleInfo: {
              name: '',
              birthDate: '',
              birthTime: ''
            },
            analysisResult: null,
            canSubmit: false
          })
          // 清除缓存
          wx.removeStorageSync('marriageAnalysisData')
        }
      }
    })
  },

  // 分享功能
  onShareAppMessage() {
    const { analysisResult, maleInfo, femaleInfo } = this.data
    
    if (analysisResult) {
      return {
        title: `${maleInfo.name}和${femaleInfo.name}的八字合婚分析结果：${analysisResult.compatibility}`,
        path: '/pages/marriage/marriage',
        imageUrl: '/assets/images/share-marriage.jpg'
      }
    }
    
    return {
      title: '八字合婚 - 测算你们的缘分指数',
      path: '/pages/marriage/marriage',
      imageUrl: '/assets/images/share-marriage.jpg'
    }
  }
}) 