/* 婚姻合配页面样式 - 基于出生信息页面风格 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

/* 顶部标题区域 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 40rpx 40rpx;
  color: white;
}

.header-content {
  flex: 1;
}

.title {
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.header-decoration {
  margin-left: 20rpx;
}

.icon {
  font-size: 80rpx;
  opacity: 0.8;
}

/* 表单容器 */
.form-container {
  background: #f8f9fa;
  border-radius: 40rpx 40rpx 0 0;
  padding: 40rpx;
  min-height: calc(100vh - 200rpx);
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.card-icon {
  font-size: 40rpx;
  opacity: 0.7;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-right: 8rpx;
}

.required {
  color: #ff4757;
  font-size: 28rpx;
  font-weight: bold;
}

.optional {
  color: #999;
  font-size: 24rpx;
  background: #f0f0f0;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 选择器 */
.picker-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 0 20rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.picker-wrapper:active {
  border-color: #667eea;
  background: white;
}

.picker-text {
  font-size: 32rpx;
  flex: 1;
}

.picker-text.placeholder {
  color: #bbb;
}

.picker-text.selected {
  color: #333;
}

.picker-icon {
  font-size: 32rpx;
  opacity: 0.5;
}

/* 提示卡片 */
.tips-card {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f39c12;
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.tips-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #d68910;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #b7950b;
  line-height: 1.4;
}

/* 提交按钮 */
.submit-section {
  margin-top: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  border-radius: 24rpx;
  font-size: 36rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s ease;
  border: none;
}

.submit-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

.submit-btn.disabled {
  background: #e8e8e8;
  color: #bbb;
  box-shadow: none;
}

.submit-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.btn-text {
  font-size: 36rpx;
}

.btn-icon {
  font-size: 32rpx;
}

/* 重新测算按钮 */
.reset-section {
  margin-bottom: 24rpx;
}

.reset-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  background: linear-gradient(135deg, #ff7043 0%, #ff8a65 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 112, 67, 0.3);
  border: none;
  transition: all 0.3s ease;
}

.reset-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 112, 67, 0.4);
}

/* 分析类型选择卡片 */
.analysis-tabs-card {
  background: white;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.analysis-tabs {
  white-space: nowrap;
  padding: 8rpx 0;
}

.analysis-tab {
  display: inline-block;
  padding: 16rpx 24rpx;
  margin-right: 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #666;
  border: 1rpx solid #e8e8e8;
  transition: all 0.3s ease;
}

.analysis-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

/* 评分区域 */
.score-section {
  text-align: center;
  padding: 20rpx 0;
}

.score-ring {
  position: relative;
  width: 240rpx;
  height: 240rpx;
  margin: 0 auto 30rpx;
  background: conic-gradient(from 0deg, #667eea 0%, #764ba2 70%, #e8e8e8 100%);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);
}

.score-ring::before {
  content: '';
  position: absolute;
  width: 180rpx;
  height: 180rpx;
  background: white;
  border-radius: 50%;
  box-shadow: inset 0 4rpx 12rpx rgba(102, 126, 234, 0.1);
}

.score-value {
  position: relative;
  z-index: 2;
  font-size: 56rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8rpx;
}

.score-desc {
  position: relative;
  z-index: 2;
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.score-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8e8e8 100%);
  border-radius: 16rpx;
  margin-top: 16rpx;
}

/* 分析详情 */
.analysis-detail {
  margin-top: 16rpx;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-score {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.detail-level {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.detail-content {
  margin-bottom: 24rpx;
}

.detail-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8e8e8 100%);
  border-radius: 16rpx;
  border-left: 4rpx solid #667eea;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.detail-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 建议区域 */
.suggestions {
  margin-top: 16rpx;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-dot {
  color: #667eea;
  margin-right: 12rpx;
  font-weight: bold;
  margin-top: 2rpx;
}

.suggestion-text {
  flex: 1;
}