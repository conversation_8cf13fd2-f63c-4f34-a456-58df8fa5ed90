/* pages/marriage/marriage.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

/* 顶部标题区域 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 40rpx 40rpx;
  color: white;
}

.header-content {
  flex: 1;
}

.title {
  font-size: 56rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.header-decoration {
  margin-left: 20rpx;
}

.icon {
  font-size: 80rpx;
  opacity: 0.8;
}

/* 表单容器 */
.form-container, .result-container {
  background: #f8f9fa;
  border-radius: 40rpx 40rpx 0 0;
  padding: 40rpx;
  min-height: calc(100vh - 200rpx);
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(102, 126, 234, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.card-icon {
  font-size: 40rpx;
  opacity: 0.7;
}

/* 表单项 */
.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-right: 8rpx;
}

.required {
  color: #ff4757;
  font-size: 28rpx;
  font-weight: bold;
}

.optional {
  color: #999;
  font-size: 24rpx;
  background: #f0f0f0;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 输入框 */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  flex: 1;
  height: 88rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 0 20rpx 0 20rpx;
  font-size: 32rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.input-placeholder {
  color: #bbb;
}

.input-icon {
  position: absolute;
  right: 20rpx;
  font-size: 32rpx;
  opacity: 0.5;
}

/* 选择器 */
.picker-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 16rpx;
  padding: 0 20rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.picker-wrapper:active {
  border-color: #667eea;
  background: white;
}

.picker-text {
  font-size: 32rpx;
  flex: 1;
}

.picker-text.placeholder {
  color: #bbb;
}

.picker-text.selected {
  color: #333;
}

.picker-icon {
  font-size: 32rpx;
  opacity: 0.5;
}

/* 提示卡片 */
.tips-card {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f39c12;
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.tips-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #d68910;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #b7950b;
  line-height: 1.4;
}

/* 提交按钮 */
.submit-section {
  margin-top: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  border-radius: 24rpx;
  font-size: 36rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s ease;
  border: none;
}

.submit-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
}

.submit-btn.disabled {
  background: #e8e8e8;
  color: #bbb;
  box-shadow: none;
}

.submit-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.btn-text {
  font-size: 36rpx;
}

.btn-icon {
  font-size: 32rpx;
}

/* 分析结果样式 */
.score-section {
  text-align: center;
  padding: 20rpx 0;
}

.score-ring {
  position: relative;
  width: 240rpx;
  height: 240rpx;
  margin: 0 auto 30rpx;
  background: conic-gradient(from 0deg, #667eea 0%, #764ba2 70%, #e8e8e8 100%);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);
}

.score-ring::before {
  content: '';
  position: absolute;
  width: 180rpx;
  height: 180rpx;
  background: white;
  border-radius: 50%;
  box-shadow: inset 0 4rpx 12rpx rgba(102, 126, 234, 0.1);
}

.score-value {
  position: relative;
  z-index: 2;
  font-size: 56rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8rpx;
}

.score-desc {
  position: relative;
  z-index: 2;
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.score-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e8e8e8 100%);
  border-radius: 16rpx;
  margin-top: 16rpx;
}

/* 八字对比样式 */
.bazi-comparison {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}

.bazi-column {
  flex: 1;
  text-align: center;
}

.person-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.bazi-pillars {
  display: flex;
  justify-content: center;
  gap: 12rpx;
}

.pillar {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 12rpx;
  min-width: 60rpx;
}

.pillar-top, .pillar-bottom {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.vs-divider {
  padding: 0 20rpx;
}

.vs-icon {
  font-size: 48rpx;
  color: #667eea;
}

/* 分析详情样式 */
.analysis-details {
  margin-top: 16rpx;
}

.analysis-item {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.analysis-item:last-child {
  margin-bottom: 0;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.analysis-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.analysis-icon {
  font-size: 28rpx;
}

.analysis-score {
  font-size: 28rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.analysis-score.high {
  background: #a8e6cf;
  color: #2d6a4f;
}

.analysis-score.medium {
  background: #ffd3b6;
  color: #9c4221;
}

.analysis-score.low {
  background: #ffaaa5;
  color: #742a2a;
}

.analysis-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.score-bar {
  height: 8rpx;
  background: #e8e8e8;
  border-radius: 4rpx;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 建议列表样式 */
.suggestions-list {
  padding: 8rpx 0;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.suggestion-item:last-child {
  margin-bottom: 0;
}

.suggestion-dot {
  color: #667eea;
  margin-right: 12rpx;
  font-weight: bold;
}

.suggestion-text {
  flex: 1;
  color: #666;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.reset-btn, .share-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  border: none;
  transition: all 0.3s ease;
}

.reset-btn {
  background: linear-gradient(135deg, #ff7043 0%, #ff8a65 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 112, 67, 0.3);
}

.share-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.reset-btn:active, .share-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}