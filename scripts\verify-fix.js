/**
 * 验证配置问题修复脚本
 * 检查所有已知问题是否已解决
 */

const fs = require('fs');
const path = require('path');

// 配置文件路径
const APP_JSON_PATH = path.join(__dirname, '../app.json');
const PROJECT_CONFIG_PATH = path.join(__dirname, '../project.config.json');

/**
 * 读取JSON文件
 */
function readJsonFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`❌ 读取文件失败: ${filePath}`, error.message);
    return null;
  }
}

/**
 * 验证修复结果
 */
function verifyFix() {
  console.log('🔍 验证配置问题修复结果...\n');
  
  let allFixed = true;
  
  // 1. 检查 app.json
  console.log('📱 检查 app.json...');
  const appJson = readJsonFile(APP_JSON_PATH);
  if (!appJson) {
    allFixed = false;
  } else {
    // 检查插件版本
    if (appJson.plugins && appJson.plugins.WechatSI) {
      const version = appJson.plugins.WechatSI.version;
      if (version === '0.3.6') {
        console.log('   ✅ 微信同声传译插件版本已更新到 0.3.6');
      } else {
        console.log(`   ❌ 插件版本仍为 ${version}，应为 0.3.6`);
        allFixed = false;
      }
    } else {
      console.log('   ⚠️  未找到微信同声传译插件配置');
    }
    
    // 检查是否移除了 wxwork 配置
    if (!appJson.wxwork) {
      console.log('   ✅ 已移除无效的 wxwork 配置');
    } else {
      console.log('   ❌ wxwork 配置仍然存在，会导致错误');
      allFixed = false;
    }
  }
  
  // 2. 检查 project.config.json
  console.log('\n⚙️  检查 project.config.json...');
  const projectConfig = readJsonFile(PROJECT_CONFIG_PATH);
  if (!projectConfig) {
    allFixed = false;
  } else {
    // 检查是否移除了 wxwork 配置
    if (!projectConfig.wxwork) {
      console.log('   ✅ 已移除无效的 wxwork 配置');
    } else {
      console.log('   ❌ wxwork 配置仍然存在，会导致错误');
      allFixed = false;
    }
    
    // 检查项目描述
    if (projectConfig.description && !projectConfig.description.includes('企业微信')) {
      console.log('   ✅ 项目描述已更新');
    } else {
      console.log('   ⚠️  项目描述可能需要更新');
    }
  }
  
  // 3. 检查企业微信配置文件
  console.log('\n🏢 检查企业微信配置文件...');
  const wxworkConfigPath = path.join(__dirname, '../wxwork.config.json');
  if (fs.existsSync(wxworkConfigPath)) {
    console.log('   ✅ 企业微信配置文件已创建');
    const wxworkConfig = readJsonFile(wxworkConfigPath);
    if (wxworkConfig && wxworkConfig.app_json_wxwork_config && wxworkConfig.project_config_wxwork) {
      console.log('   ✅ 企业微信配置内容完整');
    } else {
      console.log('   ⚠️  企业微信配置内容可能不完整');
    }
  } else {
    console.log('   ❌ 企业微信配置文件不存在');
    allFixed = false;
  }
  
  // 4. 检查配置切换脚本
  console.log('\n🔧 检查配置切换脚本...');
  const switchScriptPath = path.join(__dirname, 'switch-config.js');
  if (fs.existsSync(switchScriptPath)) {
    console.log('   ✅ 配置切换脚本已创建');
  } else {
    console.log('   ❌ 配置切换脚本不存在');
    allFixed = false;
  }
  
  // 5. 检查文档
  console.log('\n📚 检查解决方案文档...');
  const docPath = path.join(__dirname, '../docs/配置问题解决方案.md');
  if (fs.existsSync(docPath)) {
    console.log('   ✅ 解决方案文档已创建');
  } else {
    console.log('   ❌ 解决方案文档不存在');
    allFixed = false;
  }
  
  // 总结
  console.log('\n' + '='.repeat(50));
  if (allFixed) {
    console.log('🎉 所有配置问题已成功解决！');
    console.log('\n✅ 修复内容:');
    console.log('   - 微信同声传译插件版本更新到 0.3.6');
    console.log('   - 移除了无效的 wxwork 配置');
    console.log('   - 创建了企业微信配置模板');
    console.log('   - 提供了配置切换工具');
    console.log('   - 创建了详细的解决方案文档');
    
    console.log('\n📋 下一步操作:');
    console.log('   1. 重新编译项目');
    console.log('   2. 在微信开发者工具中测试');
    console.log('   3. 如需企业微信功能，使用配置切换脚本');
    
    console.log('\n🚀 项目现在应该可以正常运行了！');
  } else {
    console.log('❌ 仍有部分问题未解决，请检查上述错误信息');
  }
  
  return allFixed;
}

// 运行验证
if (require.main === module) {
  verifyFix();
}

module.exports = { verifyFix };
