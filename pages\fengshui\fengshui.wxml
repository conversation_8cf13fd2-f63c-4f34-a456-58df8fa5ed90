<!--pages/fengshui/fengshui.wxml-->
<view class="container">
  <!-- 顶部标题区域 -->
  <view class="header">
    <view class="header-content">
      <view class="title">风水布局</view>
      <view class="subtitle">为您提供专业的居家风水分析与建议</view>
    </view>
    <view class="header-decoration">
      <text class="icon">🏠</text>
    </view>
  </view>

  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 房屋信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">房屋信息</text>
        <text class="card-icon">🏘️</text>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">房屋朝向</text>
          <text class="required">*</text>
        </view>
        <picker bindchange="onDirectionChange" value="{{directionIndex}}" range="{{directions}}">
          <view class="picker-wrapper">
            <text class="picker-text {{directions[directionIndex] ? 'selected' : 'placeholder'}}">
              {{directions[directionIndex] || '请选择房屋朝向'}}
            </text>
            <text class="picker-icon">🧭</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">房屋类型</text>
          <text class="required">*</text>
        </view>
        <picker bindchange="onHouseTypeChange" value="{{houseTypeIndex}}" range="{{houseTypes}}">
          <view class="picker-wrapper">
            <text class="picker-text {{houseTypes[houseTypeIndex] ? 'selected' : 'placeholder'}}">
              {{houseTypes[houseTypeIndex] || '请选择房屋类型'}}
            </text>
            <text class="picker-icon">🏢</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">建筑年份</text>
          <text class="required">*</text>
        </view>
        <picker mode="date" fields="year" value="{{buildYear}}" bindchange="onBuildYearChange">
          <view class="picker-wrapper">
            <text class="picker-text {{buildYear ? 'selected' : 'placeholder'}}">
              {{buildYear || '请选择建筑年份'}}
            </text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 户型信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">户型信息</text>
        <text class="card-icon">📐</text>
  </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">房间数量</text>
          <text class="required">*</text>
        </view>
        <view class="room-counter">
          <view class="counter-btn {{rooms <= 1 ? 'disabled' : ''}}" bindtap="decreaseRooms">-</view>
          <text class="counter-value">{{rooms}}间</text>
          <view class="counter-btn" bindtap="increaseRooms">+</view>
        </view>
  </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">房屋面积</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <input
            class="input"
            type="digit"
            placeholder="请输入房屋面积"
            bindinput="onAreaInput"
            value="{{area}}"
            maxlength="10"
            placeholder-class="input-placeholder"
          />
          <text class="input-unit">㎡</text>
        </view>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">所在楼层</text>
          <text class="required">*</text>
        </view>
        <view class="floor-inputs">
          <view class="input-wrapper floor-input">
            <input
              class="input"
              type="number"
              placeholder="当前楼层"
              bindinput="onCurrentFloorInput"
              value="{{currentFloor}}"
              maxlength="3"
              placeholder-class="input-placeholder"
            />
          </view>
          <text class="floor-separator">/</text>
          <view class="input-wrapper floor-input">
            <input
              class="input"
              type="number"
              placeholder="总楼层"
              bindinput="onTotalFloorInput"
              value="{{totalFloor}}"
              maxlength="3"
              placeholder-class="input-placeholder"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 环境信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">环境信息</text>
        <text class="card-icon">🌳</text>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">周边环境</text>
          <text class="optional">选填</text>
        </view>
        <view class="checkbox-group">
          <view class="checkbox-item {{item.checked ? 'active' : ''}}" 
                wx:for="{{surroundings}}" 
                wx:key="value"
                bindtap="toggleSurrounding"
                data-index="{{index}}">
            <text class="checkbox-icon">{{item.icon}}</text>
            <text class="checkbox-text">{{item.label}}</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">特殊位置</text>
          <text class="optional">选填</text>
        </view>
        <view class="checkbox-group">
          <view class="checkbox-item {{item.checked ? 'active' : ''}}" 
                wx:for="{{specialLocations}}" 
                wx:key="value"
                bindtap="toggleSpecialLocation"
                data-index="{{index}}">
            <text class="checkbox-icon">{{item.icon}}</text>
            <text class="checkbox-text">{{item.label}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="tips-card">
      <view class="tips-header">
        <text class="tips-icon">💡</text>
        <text class="tips-title">温馨提示</text>
      </view>
      <view class="tips-content">
        <text class="tip-item">• 标记*的为必填项，请准确填写</text>
        <text class="tip-item">• 房屋朝向请使用指南针确认</text>
        <text class="tip-item">• 环境信息越详细，分析越准确</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button
        class="submit-btn {{canSubmit ? 'active' : 'disabled'}}"
        bindtap="analyzeFengshui"
        disabled="{{!canSubmit}}"
      >
        <text class="btn-text">开始分析</text>
        <text class="btn-icon">🔍</text>
      </button>
    </view>
  </view>

  <!-- 分析结果区域 -->
  <view class="result-container" wx:if="{{analysisResult}}">
    <!-- 总体评分 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">风水评分</text>
        <text class="card-icon">💯</text>
      </view>
      <view class="score-section">
        <view class="score-ring">
          <view class="score-value">{{analysisResult.totalScore}}分</view>
          <view class="score-desc">{{analysisResult.overall}}</view>
        </view>
        <view class="score-detail">{{analysisResult.description}}</view>
      </view>
    </view>

    <!-- 详细分析 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">详细分析</text>
        <text class="card-icon">📊</text>
      </view>
      <view class="analysis-details">
        <view class="analysis-item" wx:for="{{analysisResult.details}}" wx:key="type">
          <view class="analysis-header">
            <view class="analysis-title">
              <text class="analysis-icon">{{item.icon}}</text>
              <text>{{item.title}}</text>
            </view>
            <view class="analysis-score {{item.level}}">{{item.score}}分</view>
          </view>
          <view class="analysis-content">{{item.content}}</view>
          <view class="score-bar">
            <view class="score-fill" style="width: {{item.score}}%; background: {{item.color}}"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 改善建议 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">改善建议</text>
        <text class="card-icon">💡</text>
      </view>
      <view class="suggestions-list">
        <view class="suggestion-item" wx:for="{{analysisResult.suggestions}}" wx:key="index">
          <text class="suggestion-dot">•</text>
          <text class="suggestion-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="reset-btn" bindtap="resetAnalysis">
        <text class="btn-text">重新分析</text>
        <text class="btn-icon">🔄</text>
      </button>
      <button class="share-btn" open-type="share">
        <text class="btn-text">分享结果</text>
        <text class="btn-icon">📤</text>
      </button>
    </view>
  </view>
</view>
