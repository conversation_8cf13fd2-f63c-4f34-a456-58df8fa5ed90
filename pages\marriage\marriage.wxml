<!--pages/marriage/marriage.wxml-->
<view class="container">
  <!-- 顶部标题区域 -->
  <view class="header">
    <view class="header-content">
      <view class="title">八字合婚</view>
      <view class="subtitle">测算两人姻缘匹配度，探寻天作之合</view>
    </view>
    <view class="header-decoration">
      <text class="icon">💕</text>
    </view>
  </view>

  <!-- 表单区域 -->
  <view class="form-container">
    <!-- 男方信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">男方信息</text>
        <text class="card-icon">👨</text>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">姓名</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <input
            class="input"
            placeholder="请输入男方姓名"
            bindinput="onMaleNameInput"
            value="{{maleInfo.name}}"
            maxlength="20"
            placeholder-class="input-placeholder"
          />
          <text class="input-icon">✏️</text>
        </view>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生日期</text>
          <text class="required">*</text>
        </view>
        <picker mode="date" value="{{maleInfo.birthDate}}" bindchange="onMaleDateChange">
          <view class="picker-wrapper">
            <text class="picker-text {{maleInfo.birthDate ? 'selected' : 'placeholder'}}">
              {{maleInfo.birthDate || '请选择出生日期'}}
            </text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生时间</text>
          <text class="required">*</text>
        </view>
        <picker mode="time" value="{{maleInfo.birthTime}}" bindchange="onMaleTimeChange">
          <view class="picker-wrapper">
            <text class="picker-text {{maleInfo.birthTime ? 'selected' : 'placeholder'}}">
              {{maleInfo.birthTime || '请选择出生时间'}}
            </text>
            <text class="picker-icon">⏰</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 女方信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">女方信息</text>
        <text class="card-icon">👩</text>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">姓名</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <input
            class="input"
            placeholder="请输入女方姓名"
            bindinput="onFemaleNameInput"
            value="{{femaleInfo.name}}"
            maxlength="20"
            placeholder-class="input-placeholder"
          />
          <text class="input-icon">✏️</text>
        </view>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生日期</text>
          <text class="required">*</text>
        </view>
        <picker mode="date" value="{{femaleInfo.birthDate}}" bindchange="onFemaleDateChange">
          <view class="picker-wrapper">
            <text class="picker-text {{femaleInfo.birthDate ? 'selected' : 'placeholder'}}">
              {{femaleInfo.birthDate || '请选择出生日期'}}
            </text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生时间</text>
          <text class="required">*</text>
        </view>
        <picker mode="time" value="{{femaleInfo.birthTime}}" bindchange="onFemaleTimeChange">
          <view class="picker-wrapper">
            <text class="picker-text {{femaleInfo.birthTime ? 'selected' : 'placeholder'}}">
              {{femaleInfo.birthTime || '请选择出生时间'}}
            </text>
            <text class="picker-icon">⏰</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="tips-card">
      <view class="tips-header">
        <text class="tips-icon">💡</text>
        <text class="tips-title">温馨提示</text>
      </view>
      <view class="tips-content">
        <text class="tip-item">• 姓名和出生时间为必填项</text>
        <text class="tip-item">• 出生时间越精确，合婚结果越准确</text>
        <text class="tip-item">• 您的信息将被安全保护，仅用于合婚分析</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button
        class="submit-btn {{canSubmit ? 'active' : 'disabled'}}"
        bindtap="startAnalysis"
        disabled="{{!canSubmit}}"
      >
        <text class="btn-text">开始合婚测算</text>
        <text class="btn-icon">💕</text>
      </button>
    </view>
  </view>

  <!-- 分析结果区域 -->
  <view class="result-container" wx:if="{{analysisResult}}">
    <!-- 总体评分 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">匹配评分</text>
        <text class="card-icon">💯</text>
      </view>
      <view class="score-section">
        <view class="score-ring">
          <view class="score-value">{{analysisResult.totalScore}}分</view>
          <view class="score-desc">{{analysisResult.compatibility}}</view>
        </view>
        <view class="score-detail">{{analysisResult.description}}</view>
      </view>
    </view>

    <!-- 八字对比 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">八字对比</text>
        <text class="card-icon">🔮</text>
      </view>
      <view class="bazi-comparison">
        <view class="bazi-column">
          <view class="person-name">{{maleInfo.name}}</view>
          <view class="bazi-pillars">
            <view class="pillar" wx:for="{{analysisResult.maleBazi}}" wx:key="index">
              <view class="pillar-top">{{item.tiangan}}</view>
              <view class="pillar-bottom">{{item.dizhi}}</view>
            </view>
          </view>
        </view>
        <view class="vs-divider">
          <text class="vs-icon">💕</text>
        </view>
        <view class="bazi-column">
          <view class="person-name">{{femaleInfo.name}}</view>
          <view class="bazi-pillars">
            <view class="pillar" wx:for="{{analysisResult.femaleBazi}}" wx:key="index">
              <view class="pillar-top">{{item.tiangan}}</view>
              <view class="pillar-bottom">{{item.dizhi}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细分析 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">详细分析</text>
        <text class="card-icon">📊</text>
      </view>
      <view class="analysis-details">
        <view class="analysis-item" wx:for="{{analysisResult.details}}" wx:key="type">
          <view class="analysis-header">
            <view class="analysis-title">
              <text class="analysis-icon">{{item.icon}}</text>
              <text>{{item.title}}</text>
            </view>
            <view class="analysis-score {{item.level}}">{{item.score}}分</view>
          </view>
          <view class="analysis-content">{{item.content}}</view>
          <view class="score-bar">
            <view class="score-fill" style="width: {{item.score}}%; background: {{item.color}}"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 婚姻建议 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">婚姻建议</text>
        <text class="card-icon">💡</text>
      </view>
      <view class="suggestions-list">
        <view class="suggestion-item" wx:for="{{analysisResult.suggestions}}" wx:key="index">
          <text class="suggestion-dot">•</text>
          <text class="suggestion-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="reset-btn" bindtap="resetAnalysis">
        <text class="btn-text">重新测算</text>
        <text class="btn-icon">🔄</text>
      </button>
      <button class="share-btn" open-type="share">
        <text class="btn-text">分享结果</text>
        <text class="btn-icon">📤</text>
      </button>
    </view>
  </view>
</view> 