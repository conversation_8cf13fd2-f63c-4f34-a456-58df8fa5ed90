/**
 * 统一导航管理工具
 * 处理页面间跳转、出生信息验证、用户状态检查等
 */

// 需要出生信息的页面列表
const BIRTH_INFO_REQUIRED_PAGES = [
  '/pages/bazi/bazi',           // 八字分析
  '/pages/ziwei/ziwei',         // 紫薇斗数
  '/pages/wuxing/wuxing',       // 五行分析
  '/pages/yijing/yijing',       // 易经占卜
  '/pages/fortune/fortune',     // 运势预测
  '/pages/marriage/marriage',   // 合婚测算
  '/pages/name-test/name-test', // 姓名测算
  '/pages/hehun/hehun'         // 八字合婚
]

// 需要登录的页面列表
const LOGIN_REQUIRED_PAGES = [
  '/pages/recharge/recharge',
  '/pages/admin/admin',
  '/pages/approval/approval'
]

// 页面配置
const pageConfig = {
  // 基础页面
  'index': '/pages/index/index',
  'profile': '/pages/profile/profile',
  'ai-chat': '/pages/ai-chat/ai-chat',
  
  // 功能页面
  'bazi': '/pages/bazi/bazi',
  'wuxing': '/pages/wuxing/wuxing',
  'yijing': '/pages/yijing/yijing',
  'fengshui': '/pages/fengshui/fengshui',
  'fortune': '/pages/fortune/fortune',
  'ziwei': '/pages/ziwei/ziwei',
  'marriage': '/pages/marriage/marriage',
  'name-test': '/pages/name-test/name-test',
  
  // 用户中心
  'birth-info': '/pages/birth-info/birth-info',
  'customer-service': '/pages/customer-service/customer-service',
  'feedback': '/pages/feedback/feedback',
  'recharge': '/pages/recharge/recharge',
  'sign-in': '/pages/sign-in/sign-in',
  
  // 其他页面
  'post-detail': '/pages/post-detail/post-detail',
  'divination': '/pages/divination/divination',
  'daily-fortune': '/pages/daily-fortune/daily-fortune',
  'chat-list': '/pages/chat-list/chat-list',
  'admin': '/pages/admin/admin'
}

class NavigationManager {
  constructor() {
    this.app = getApp()
  }

  /**
   * 统一页面跳转方法
   * @param {string} url - 目标页面路径
   * @param {Object} options - 跳转选项
   * @param {string} options.method - 跳转方法 (navigateTo|redirectTo|switchTab|reLaunch)
   * @param {Object} options.params - 页面参数
   * @param {Function} options.success - 成功回调
   * @param {Function} options.fail - 失败回调
   */
  async navigateTo(url, options = {}) {
    try {
      const {
        method = 'navigateTo',
        params = {},
        success,
        fail
      } = options

      // 构建完整URL
      const fullUrl = this.buildUrl(url, params)

      // 检查页面访问权限
      const checkResult = await this.checkPageAccess(fullUrl)
      if (!checkResult.allowed) {
        this.handleAccessDenied(checkResult, fullUrl)
        return
      }

      // 执行跳转
      const navigateOptions = {
        url: fullUrl,
        success: (res) => {
          console.log(`页面跳转成功: ${fullUrl}`)
          success && success(res)
        },
        fail: (error) => {
          console.error(`页面跳转失败: ${fullUrl}`, error)
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
          fail && fail(error)
        }
      }

      switch (method) {
        case 'navigateTo':
          wx.navigateTo(navigateOptions)
          break
        case 'redirectTo':
          wx.redirectTo(navigateOptions)
          break
        case 'switchTab':
          wx.switchTab(navigateOptions)
          break
        case 'reLaunch':
          wx.reLaunch(navigateOptions)
          break
        default:
          wx.navigateTo(navigateOptions)
      }

    } catch (error) {
      console.error('导航异常:', error)
      wx.showToast({
        title: '系统异常',
        icon: 'none'
      })
    }
  }

  /**
   * 检查页面访问权限
   * @param {string} url - 页面路径
   * @returns {Object} 检查结果
   */
  async checkPageAccess(url) {
    const cleanUrl = url.split('?')[0] // 移除参数

    // 检查出生信息要求
    if (BIRTH_INFO_REQUIRED_PAGES.includes(cleanUrl)) {
      const birthInfo = wx.getStorageSync('birthInfo')
      if (!birthInfo) {
        return {
          allowed: false,
          reason: 'BIRTH_INFO_REQUIRED',
          message: '该功能需要您的出生信息才能提供准确分析',
          action: 'GOTO_BIRTH_INFO'
        }
      }
    }

    // 检查登录要求
    if (LOGIN_REQUIRED_PAGES.includes(cleanUrl)) {
      const isLoggedIn = this.checkLoginStatus()
      if (!isLoggedIn) {
        return {
          allowed: false,
          reason: 'LOGIN_REQUIRED',
          message: '该功能需要登录后才能使用',
          action: 'GOTO_LOGIN'
        }
      }
    }

    return { allowed: true }
  }

  /**
   * 处理访问被拒绝的情况
   * @param {Object} checkResult - 检查结果
   * @param {string} targetUrl - 目标页面URL
   */
  handleAccessDenied(checkResult, targetUrl) {
    const { reason, message, action } = checkResult

    switch (action) {
      case 'GOTO_BIRTH_INFO':
        // 保存目标页面，以便填写完出生信息后跳转
        if (targetUrl) {
          wx.setStorageSync('targetPage', targetUrl)
        }

        wx.showModal({
          title: '需要出生信息',
          content: message + '，是否前往填写？',
          confirmText: '去填写',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/birth-info/birth-info'
              })
            }
          }
        })
        break

      case 'GOTO_LOGIN':
        wx.showModal({
          title: '需要登录',
          content: message + '，是否前往登录？',
          confirmText: '去登录',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 这里可以调用登录逻辑
              this.handleLogin()
            }
          }
        })
        break

      case 'SHOW_TIP':
        wx.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        })
        break

      default:
        wx.showToast({
          title: message || '访问受限',
          icon: 'none'
        })
    }
  }

  /**
   * 构建URL
   * @param {string} url - 基础URL
   * @param {Object} params - 参数对象
   * @returns {string} 完整URL
   */
  buildUrl(url, params) {
    if (!params || Object.keys(params).length === 0) {
      return url
    }

    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&')

    return `${url}?${queryString}`
  }

  /**
   * 检查登录状态
   * @returns {boolean} 是否已登录
   */
  checkLoginStatus() {
    // 这里可以根据实际的登录状态检查逻辑来实现
    const userInfo = wx.getStorageSync('user_info')
    const accessToken = wx.getStorageSync('access_token')
    return !!(userInfo && accessToken)
  }

  /**
   * 处理登录
   */
  async handleLogin() {
    try {
      // 这里可以调用实际的登录逻辑
      console.log('开始登录流程')
      // 示例：跳转到登录页面或调用登录API
    } catch (error) {
      console.error('登录失败:', error)
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      })
    }
  }

  /**
   * 返回上一页
   * @param {number} delta - 返回层数
   */
  navigateBack(delta = 1) {
    wx.navigateBack({ delta })
  }

  /**
   * 跳转到TabBar页面
   * @param {string} url - TabBar页面路径
   */
  switchTab(url) {
    wx.switchTab({ url })
  }
}

// 创建单例实例
const navigationManager = new NavigationManager()

module.exports = navigationManager
