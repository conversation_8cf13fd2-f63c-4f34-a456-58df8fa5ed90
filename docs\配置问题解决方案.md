# 配置问题解决方案

## 问题描述

在开发过程中遇到以下问题：
1. `无效的 app.json ["wxwork"]` - 企业微信配置在普通微信小程序环境中无效
2. `插件更新提示` - 微信同声传译插件需要更新到最新版本
3. `SharedArrayBuffer` 警告 - 浏览器兼容性警告

## 解决方案

### 1. 插件版本更新 ✅

**问题**: 微信同声传译插件版本过期
**解决**: 已将插件版本从 0.3.5 更新到 0.3.6

```json
"plugins": {
  "WechatSI": {
    "version": "0.3.6",
    "provider": "wx069ba97219f66d99"
  }
}
```

### 2. 企业微信配置问题 ✅

**问题**: `wxwork` 配置在普通微信小程序开发者工具中无效
**解决**: 
- 从 `app.json` 和 `project.config.json` 中移除了 `wxwork` 配置
- 创建了独立的 `wxwork.config.json` 文件保存企业微信配置
- 提供了配置切换脚本 `scripts/switch-config.js`

### 3. 配置切换机制

#### 3.1 文件结构
```
├── app.json                    # 普通微信小程序配置
├── project.config.json         # 普通微信小程序项目配置
├── wxwork.config.json          # 企业微信配置模板
└── scripts/switch-config.js    # 配置切换脚本
```

#### 3.2 使用方法

**切换到企业微信配置:**
```bash
node scripts/switch-config.js wxwork
```

**切换回普通微信小程序配置:**
```bash
node scripts/switch-config.js wechat
```

**查看帮助:**
```bash
node scripts/switch-config.js help
```

### 4. SharedArrayBuffer 警告

**问题**: 浏览器兼容性警告
**说明**: 这是一个浏览器警告，不影响小程序功能，可以忽略

**如果需要解决，可以在服务器配置中添加以下响应头:**
```
Cross-Origin-Opener-Policy: same-origin
Cross-Origin-Embedder-Policy: require-corp
```

## 当前状态

✅ **已解决的问题:**
- 插件版本更新完成
- 企业微信配置问题解决
- 配置切换机制建立

✅ **当前配置状态:**
- 项目配置为普通微信小程序模式
- 所有无效配置已移除
- 企业微信配置已保存在独立文件中

## 开发建议

### 1. 日常开发
- 使用当前的普通微信小程序配置进行开发
- 在微信开发者工具中测试功能

### 2. 企业微信开发
- 使用配置切换脚本切换到企业微信模式
- 在企业微信开发者工具中进行测试
- 记得填写正确的企业微信参数（corpid、agentid、secretKey）

### 3. 版本控制
- 普通微信小程序配置文件正常提交
- 企业微信敏感配置信息不要提交到版本控制
- 使用 `.gitignore` 忽略包含敏感信息的配置文件

## 注意事项

1. **配置切换前备份**: 脚本会自动创建备份，但建议手动备份重要配置
2. **企业微信参数**: 切换到企业微信配置后，需要填写正确的企业参数
3. **开发工具**: 企业微信功能需要在企业微信开发者工具中测试
4. **权限配置**: 企业微信功能需要相应的企业权限配置

## 故障排除

### 如果配置切换失败
1. 检查文件权限
2. 手动恢复备份文件
3. 重新运行切换脚本

### 如果企业微信功能异常
1. 检查企业微信参数配置
2. 确认企业权限设置
3. 使用企业微信开发者工具调试

### 如果插件功能异常
1. 确认插件版本是否为最新
2. 检查插件权限配置
3. 重新编译项目

## 联系支持

如果遇到其他问题，请：
1. 查看微信小程序官方文档
2. 检查企业微信开发文档
3. 联系技术支持团队
