/**
 * 错误处理和用户体验优化工具
 * 统一处理各种错误情况，提供友好的用户提示
 */

class ErrorHandler {
  constructor() {
    this.errorCodes = {
      // 网络错误
      'NETWORK_ERROR': '网络连接失败，请检查网络设置',
      'TIMEOUT': '请求超时，请稍后重试',
      'SERVER_ERROR': '服务器错误，请稍后重试',
      
      // 认证错误
      'AUTH_FAILED': '登录已过期，请重新登录',
      'PERMISSION_DENIED': '没有权限执行此操作',
      'TOKEN_EXPIRED': '登录状态已过期，请重新登录',
      
      // 业务错误
      'BIRTH_INFO_REQUIRED': '需要填写出生信息',
      'LOGIN_REQUIRED': '需要登录后使用',
      'WXWORK_REQUIRED': '该功能仅在企业微信中可用',
      'INSUFFICIENT_POINTS': '积分不足',
      
      // 数据错误
      'DATA_NOT_FOUND': '数据不存在',
      'INVALID_PARAMS': '无效的参数',
      'DATA_FORMAT_ERROR': '数据格式错误',
      
      // 系统错误
      'UNKNOWN_ERROR': '未知错误，请稍后重试',
      'PAGE_NOT_FOUND': '页面不存在',
      'FUNCTION_NOT_AVAILABLE': '功能暂不可用',
      'API_ERROR': 'API调用失败，请稍后重试',
      'AUTH_REQUIRED': '请先登录',
      'RESOURCE_NOT_FOUND': '请求的资源不存在'
    }
    
    this.retryableErrors = [
      'NETWORK_ERROR',
      'TIMEOUT',
      'SERVER_ERROR'
    ]
  }

  /**
   * 处理错误
   * @param {Error|string} error - 错误对象或错误代码
   * @param {Object} options - 处理选项
   * @param {boolean} options.showToast - 是否显示Toast提示
   * @param {boolean} options.showModal - 是否显示Modal提示
   * @param {Function} options.onRetry - 重试回调函数
   * @param {Function} options.onCancel - 取消回调函数
   * @param {string} options.customMessage - 自定义错误消息
   */
  handle(error, options = {}) {
    const {
      showToast = true,
      showModal = false,
      onRetry,
      onCancel,
      customMessage
    } = options

    const errorInfo = this.parseError(error)
    const message = customMessage || errorInfo.message
    const isRetryable = this.retryableErrors.includes(errorInfo.code)

    console.error('错误处理:', errorInfo)

    if (showModal || (isRetryable && onRetry)) {
      this.showErrorModal(message, isRetryable, onRetry, onCancel)
    } else if (showToast) {
      this.showErrorToast(message)
    }

    // 记录错误日志
    this.logError(errorInfo)

    return errorInfo
  }

  /**
   * 解析错误
   * @param {Error|string} error - 错误对象或错误代码
   * @returns {Object} 错误信息
   */
  parseError(error) {
    let code = 'UNKNOWN_ERROR'
    let message = this.errorCodes['UNKNOWN_ERROR']
    let originalError = error

    if (typeof error === 'string') {
      code = error
      message = this.errorCodes[error] || error
    } else if (error instanceof Error) {
      message = error.message
      
      // 根据错误消息判断错误类型
      if (message.includes('网络') || message.includes('network')) {
        code = 'NETWORK_ERROR'
        message = this.errorCodes['NETWORK_ERROR']
      } else if (message.includes('超时') || message.includes('timeout')) {
        code = 'TIMEOUT'
        message = this.errorCodes['TIMEOUT']
      } else if (message.includes('401') || message.includes('unauthorized')) {
        code = 'AUTH_FAILED'
        message = this.errorCodes['AUTH_FAILED']
      } else if (message.includes('403') || message.includes('forbidden')) {
        code = 'PERMISSION_DENIED'
        message = this.errorCodes['PERMISSION_DENIED']
      } else if (message.includes('404') || message.includes('not found')) {
        code = 'DATA_NOT_FOUND'
        message = this.errorCodes['DATA_NOT_FOUND']
      } else if (message.includes('500') || message.includes('server')) {
        code = 'SERVER_ERROR'
        message = this.errorCodes['SERVER_ERROR']
      }
    }

    return {
      code,
      message,
      originalError,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 显示错误Toast
   * @param {string} message - 错误消息
   */
  showErrorToast(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    })
  }

  /**
   * 显示错误Modal
   * @param {string} message - 错误消息
   * @param {boolean} isRetryable - 是否可重试
   * @param {Function} onRetry - 重试回调
   * @param {Function} onCancel - 取消回调
   */
  showErrorModal(message, isRetryable, onRetry, onCancel) {
    const options = {
      title: '提示',
      content: message,
      showCancel: isRetryable,
      confirmText: isRetryable ? '重试' : '确定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm && onRetry) {
          onRetry()
        } else if (res.cancel && onCancel) {
          onCancel()
        }
      }
    }

    wx.showModal(options)
  }

  /**
   * 记录错误日志
   * @param {Object} errorInfo - 错误信息
   */
  logError(errorInfo) {
    try {
      // 获取当前页面信息
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const pageRoute = currentPage ? currentPage.route : 'unknown'

      // 构建日志对象
      const logEntry = {
        ...errorInfo,
        pageRoute,
        userAgent: wx.getSystemInfoSync(),
        timestamp: new Date().toISOString()
      }

      // 保存到本地存储（可选）
      const errorLogs = wx.getStorageSync('error_logs') || []
      errorLogs.unshift(logEntry)
      
      // 只保留最近50条错误日志
      if (errorLogs.length > 50) {
        errorLogs.splice(50)
      }
      
      wx.setStorageSync('error_logs', errorLogs)

      // 这里可以添加上报到服务器的逻辑
      // this.reportToServer(logEntry)
      
    } catch (e) {
      console.error('记录错误日志失败:', e)
    }
  }

  /**
   * 上报错误到服务器（可选实现）
   * @param {Object} logEntry - 日志条目
   */
  async reportToServer(logEntry) {
    try {
      // 这里实现上报逻辑
      // await api.reportError(logEntry)
    } catch (e) {
      console.error('上报错误失败:', e)
    }
  }

  /**
   * 获取错误日志
   * @param {number} limit - 限制数量
   * @returns {Array} 错误日志列表
   */
  getErrorLogs(limit = 10) {
    const logs = wx.getStorageSync('error_logs') || []
    return logs.slice(0, limit)
  }

  /**
   * 清除错误日志
   */
  clearErrorLogs() {
    wx.removeStorageSync('error_logs')
  }

  /**
   * 处理API错误的便捷方法
   * @param {Error} error - API错误
   * @param {Function} onRetry - 重试回调
   */
  handleApiError(error, onRetry) {
    return this.handle(error, {
      showModal: true,
      onRetry
    })
  }

  /**
   * 处理页面跳转错误的便捷方法
   * @param {Error} error - 跳转错误
   */
  handleNavigationError(error) {
    return this.handle(error, {
      showToast: true,
      customMessage: '页面跳转失败，请重试'
    })
  }

  /**
   * 处理权限错误的便捷方法
   * @param {string} feature - 功能名称
   * @param {Function} onAuth - 授权回调
   */
  handlePermissionError(feature, onAuth) {
    return this.handle('PERMISSION_DENIED', {
      showModal: true,
      customMessage: `使用${feature}功能需要相应权限，是否前往授权？`,
      onRetry: onAuth
    })
  }
}

// 创建单例实例
const errorHandler = new ErrorHandler()

module.exports = errorHandler
