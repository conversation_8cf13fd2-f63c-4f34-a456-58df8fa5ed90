Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#9575cd",
    list: [
      {
        pagePath: "/pages/index/index",
        text: "首页",
        iconPath: "https://img.icons8.com/fluency-systems-regular/96/999999/home.png",
        selectedIconPath: "https://img.icons8.com/fluency-systems-filled/96/9575cd/home.png"
      },
      {
        pagePath: "/pages/ai-chat/ai-chat",
        text: "AI问答",
        iconPath: "/assets/icons/fortune.png",
        selectedIconPath: "/assets/icons/fortune.png",
        isSpecial: true
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        iconPath: "https://img.icons8.com/fluency-systems-regular/96/999999/user-male-circle.png",
        selectedIconPath: "https://img.icons8.com/fluency-systems-filled/96/9575cd/user-male-circle.png"
      }
    ],
    initialized: false
  },
  attached() {
    console.log('Custom tabBar attached')
    // 延迟执行，确保页面已经完全加载
    this.initTabBar()
  },
  ready() {
    console.log('Custom tabBar ready')
    // 组件完全准备好后再次尝试设置选中状态
    this.initTabBar()
  },
  show() {
    console.log('Custom tabBar show')
    // 页面显示时更新选中状态
    this.setSelected()
  },
  methods: {
    // 初始化 tabBar
    initTabBar() {
      if (this.data.initialized) {
        return
      }

      // 多次尝试初始化，确保页面栈已准备好
      let attempts = 0
      const maxAttempts = 5

      const tryInit = () => {
        attempts++
        console.log(`TabBar init attempt ${attempts}`)

        if (this.setSelected() || attempts >= maxAttempts) {
          this.setData({ initialized: true })
          console.log('TabBar initialized successfully')
          return
        }

        // 如果失败，延迟重试
        setTimeout(tryInit, 200 * attempts)
      }

      tryInit()
    },

    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      console.log('Switching to tab:', url)

      wx.switchTab({
        url,
        success: () => {
          this.setData({
            selected: data.index
          })
        },
        fail: (err) => {
          console.error('Switch tab failed:', err)
        }
      })
    },
    setSelected() {
      try {
        const pages = getCurrentPages()
        console.log('Current pages:', pages?.length || 0)

        if (!pages || pages.length === 0) {
          console.warn('No pages found, using default selected index')
          // 设置默认选中状态，但返回 false 表示初始化未完成
          this.setData({ selected: 0 })
          return false
        }

        const currentPage = pages[pages.length - 1]
        if (!currentPage || !currentPage.route) {
          console.warn('Current page or route not found, using default selected index')
          this.setData({ selected: 0 })
          return false
        }

        const currentRoute = '/' + currentPage.route
        console.log('Current route:', currentRoute)

        // 更精确的路径匹配
        const selected = this.data.list.findIndex(item => {
          const tabPath = item.pagePath
          console.log('Comparing:', currentRoute, 'with', tabPath)

          // 精确匹配
          if (currentRoute === tabPath) {
            return true
          }

          // 特殊处理 ai-chat 页面
          if (currentRoute.includes('ai-chat') && tabPath.includes('ai-chat')) {
            return true
          }

          return false
        })

        console.log('Selected tab index:', selected)

        if (selected !== -1) {
          this.setData({ selected })
          return true
        } else {
          console.warn('No matching tab found for route:', currentRoute)
          // 如果没有匹配的 tab，设置为首页
          this.setData({ selected: 0 })
          return true
        }
      } catch (error) {
        console.error('Error in setSelected:', error)
        // 使用默认选中状态
        this.setData({ selected: 0 })
        return false
      }
    }
  }
}) 