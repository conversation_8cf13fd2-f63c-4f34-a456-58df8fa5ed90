<?xml version="1.0" encoding="UTF-8"?>
<view class="container">
  <!-- 顶部标题区域 -->
  <view class="header" wx:if="{{!showResult}}">
    <view class="header-content">
      <view class="title">八字合婚</view>
      <view class="subtitle">测算两人姻缘匹配度，探寻天作之合</view>
    </view>
    <view class="header-decoration">
      <text class="icon">💕</text>
    </view>
  </view>

  <!-- 表单区域 -->
  <view class="form-container" wx:if="{{!showResult}}">
    <!-- 男方信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">男方信息</text>
        <text class="card-icon">👨</text>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生日期</text>
          <text class="required">*</text>
        </view>
        <picker mode="date" value="{{male.date}}" bindchange="bindMaleDateChange">
          <view class="picker-wrapper">
            <text class="picker-text {{male.date ? 'selected' : 'placeholder'}}">
              {{male.date || '请选择出生日期'}}
            </text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生时间</text>
          <text class="required">*</text>
        </view>
        <picker mode="time" value="{{male.time}}" bindchange="bindMaleTimeChange">
          <view class="picker-wrapper">
            <text class="picker-text {{male.time ? 'selected' : 'placeholder'}}">
              {{male.time || '请选择出生时间'}}
            </text>
            <text class="picker-icon">⏰</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生地点</text>
          <text class="optional">选填</text>
        </view>
        <view class="picker-wrapper" bindtap="chooseMaleLocation">
          <text class="picker-text {{male.location ? 'selected' : 'placeholder'}}">
            {{male.location || '点击选择出生地点'}}
          </text>
          <text class="picker-icon">📍</text>
        </view>
      </view>
    </view>

    <!-- 女方信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <text class="card-title">女方信息</text>
        <text class="card-icon">👩</text>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生日期</text>
          <text class="required">*</text>
        </view>
        <picker mode="date" value="{{female.date}}" bindchange="bindFemaleDateChange">
          <view class="picker-wrapper">
            <text class="picker-text {{female.date ? 'selected' : 'placeholder'}}">
              {{female.date || '请选择出生日期'}}
            </text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生时间</text>
          <text class="required">*</text>
        </view>
        <picker mode="time" value="{{female.time}}" bindchange="bindFemaleTimeChange">
          <view class="picker-wrapper">
            <text class="picker-text {{female.time ? 'selected' : 'placeholder'}}">
              {{female.time || '请选择出生时间'}}
            </text>
            <text class="picker-icon">⏰</text>
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="item-header">
          <text class="label">出生地点</text>
          <text class="optional">选填</text>
        </view>
        <view class="picker-wrapper" bindtap="chooseFemaleLocation">
          <text class="picker-text {{female.location ? 'selected' : 'placeholder'}}">
            {{female.location || '点击选择出生地点'}}
          </text>
          <text class="picker-icon">📍</text>
        </view>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="tips-card">
      <view class="tips-header">
        <text class="tips-icon">💡</text>
        <text class="tips-title">温馨提示</text>
      </view>
      <view class="tips-content">
        <text class="tip-item">• 出生日期和时间为必填项</text>
        <text class="tip-item">• 出生时间越精确，合婚结果越准确</text>
        <text class="tip-item">• 您的信息将被安全保护，仅用于合婚分析</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button
        class="submit-btn {{canSubmit ? 'active' : 'disabled'}}"
        bindtap="submitAnalysis"
        disabled="{{!canSubmit}}"
        loading="{{loading}}"
      >
        <text class="btn-text">开始合婚测算</text>
        <text class="btn-icon">💕</text>
      </button>
    </view>
  </view>

  <!-- 结果页面顶部标题区域 -->
  <view class="header" wx:if="{{showResult}}">
    <view class="header-content">
      <view class="title">合婚结果</view>
      <view class="subtitle">根据双方八字分析得出的匹配度</view>
    </view>
    <view class="header-decoration">
      <text class="icon">✨</text>
    </view>
  </view>

  <!-- 结果区域 -->
  <view class="form-container" wx:if="{{showResult}}">
    <!-- 重新测算按钮 -->
    <view class="reset-section">
      <button class="reset-btn" bindtap="resetAnalysis">
        <text class="btn-text">重新测算</text>
        <text class="btn-icon">🔄</text>
      </button>
    </view>

    <!-- 分析类型选择 -->
    <view class="analysis-tabs-card">
      <view class="card-header">
        <text class="card-title">分析维度</text>
        <text class="card-icon">📊</text>
      </view>
      <scroll-view class="analysis-tabs" scroll-x="true">
        <view
          class="analysis-tab {{currentType === item.id ? 'active' : ''}}"
          wx:for="{{analysisTypes}}"
          wx:key="id"
          bindtap="switchAnalysisType"
          data-type="{{item.id}}"
        >
          {{item.name}}
        </view>
      </scroll-view>
    </view>

    <!-- 总体评分卡片 -->
    <view class="info-card" wx:if="{{currentType === 'overall'}}">
      <view class="card-header">
        <text class="card-title">总体匹配度</text>
        <text class="card-icon">💯</text>
      </view>
      <view class="score-section">
        <view class="score-ring">
          <view class="score-value">{{analysis.overall.score}}分</view>
          <view class="score-desc">{{analysis.overall.level}}</view>
        </view>
        <view class="score-detail">{{analysis.overall.description}}</view>
      </view>
    </view>

    <!-- 各项分析卡片 -->
    <view class="info-card" wx:if="{{currentType !== 'overall'}}">
      <view class="card-header">
        <text class="card-title">{{analysisTypes[currentTypeIndex].name}}分析</text>
        <text class="card-icon">📋</text>
      </view>

      <view class="analysis-detail">
        <view class="detail-header">
          <view class="detail-score">匹配度：{{analysis[currentType].score}}分</view>
          <view class="detail-level">{{analysis[currentType].level}}</view>
        </view>

        <view class="detail-content">
          <view class="detail-item" wx:for="{{analysis[currentType].details}}" wx:key="title">
            <view class="detail-title">{{item.title}}</view>
            <view class="detail-desc">{{item.content}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 建议卡片 -->
    <view class="info-card" wx:if="{{currentType !== 'overall' && analysis[currentType].suggestions}}">
      <view class="card-header">
        <text class="card-title">专业建议</text>
        <text class="card-icon">💡</text>
      </view>
      <view class="suggestions">
        <view class="suggestion-item" wx:for="{{analysis[currentType].suggestions}}" wx:key="index">
          <text class="suggestion-dot">•</text>
          <text class="suggestion-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 分享按钮 -->
    <view class="submit-section">
      <button class="submit-btn active" open-type="share">
        <text class="btn-text">分享测算结果</text>
        <text class="btn-icon">📤</text>
      </button>
    </view>
  </view>
</view>