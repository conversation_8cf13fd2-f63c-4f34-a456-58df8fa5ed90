// app.js
const util = require('./utils/util')

App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 普通微信登录
    this.normalLogin()
  },

  // 普通微信登录
  normalLogin() {
    wx.login({
      success: res => {
        console.log('微信登录成功', res)
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },

  // 添加图片加载失败处理
  onError(err) {
    console.error('小程序错误：', err);
    
    // 图片加载失败时使用默认图片
    if (err.includes('Failed to load local image resource')) {
      const defaultImages = {
        '/assets/icons/home/<USER>': '/assets/icons/home/<USER>',
        '/assets/icons/home/<USER>': '/assets/icons/home/<USER>',
        '/assets/images/article1.jpg': '/assets/images/banner1.jpg',
        '/assets/images/article2.jpg': '/assets/images/banner2.jpg',
        '/assets/images/article3.jpg': '/assets/images/banner3.jpg',
        '/assets/images/empty-contacts.png': '/assets/images/default-avatar.png'
      };
      
      // 处理profile目录下的图片
      if (err.includes('/assets/icons/profile/')) {
        return '/assets/images/default-avatar.png';
      }
      
      // 返回对应的默认图片
      for (const [errorPath, defaultPath] of Object.entries(defaultImages)) {
        if (err.includes(errorPath)) {
          return defaultPath;
        }
      }
    }
  },

  // 全局数据
  globalData: {
    userInfo: null,
    util: util
  }
})
