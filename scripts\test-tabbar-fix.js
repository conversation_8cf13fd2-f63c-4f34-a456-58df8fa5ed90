/**
 * 测试 TabBar 修复脚本
 * 验证自定义 TabBar 配置是否正确
 */

const fs = require('fs');
const path = require('path');

// 文件路径
const CUSTOM_TABBAR_PATH = path.join(__dirname, '../custom-tab-bar/index.js');
const APP_JSON_PATH = path.join(__dirname, '../app.json');
const INDEX_PAGE_PATH = path.join(__dirname, '../pages/index/index.js');
const AI_CHAT_PAGE_PATH = path.join(__dirname, '../pages/ai-chat/ai-chat.js');
const PROFILE_PAGE_PATH = path.join(__dirname, '../pages/profile/profile.js');

/**
 * 读取文件内容
 */
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`❌ 读取文件失败: ${filePath}`, error.message);
    return null;
  }
}

/**
 * 检查 JSON 文件
 */
function checkJsonFile(filePath) {
  const content = readFile(filePath);
  if (!content) return false;
  
  try {
    JSON.parse(content);
    return true;
  } catch (error) {
    console.error(`❌ JSON 格式错误: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 检查自定义 TabBar 配置
 */
function checkCustomTabBar() {
  console.log('🔍 检查自定义 TabBar 配置...');
  
  const content = readFile(CUSTOM_TABBAR_PATH);
  if (!content) return false;
  
  let allChecks = true;
  
  // 检查是否包含必要的方法
  const requiredMethods = ['initTabBar', 'setSelected', 'switchTab'];
  requiredMethods.forEach(method => {
    if (content.includes(method)) {
      console.log(`   ✅ 包含 ${method} 方法`);
    } else {
      console.log(`   ❌ 缺少 ${method} 方法`);
      allChecks = false;
    }
  });
  
  // 检查是否包含初始化标志
  if (content.includes('initialized: false')) {
    console.log('   ✅ 包含初始化状态标志');
  } else {
    console.log('   ❌ 缺少初始化状态标志');
    allChecks = false;
  }
  
  // 检查是否包含重试机制
  if (content.includes('maxAttempts') && content.includes('tryInit')) {
    console.log('   ✅ 包含重试机制');
  } else {
    console.log('   ❌ 缺少重试机制');
    allChecks = false;
  }
  
  // 检查是否包含详细的日志
  if (content.includes('console.log') && content.includes('Current route:')) {
    console.log('   ✅ 包含详细调试日志');
  } else {
    console.log('   ⚠️  建议添加更多调试日志');
  }
  
  return allChecks;
}

/**
 * 检查 app.json 配置
 */
function checkAppJson() {
  console.log('\n📱 检查 app.json 配置...');
  
  if (!checkJsonFile(APP_JSON_PATH)) return false;
  
  const content = readFile(APP_JSON_PATH);
  const appConfig = JSON.parse(content);
  
  let allChecks = true;
  
  // 检查 tabBar 配置
  if (appConfig.tabBar) {
    if (appConfig.tabBar.custom === true) {
      console.log('   ✅ 启用了自定义 tabBar');
    } else {
      console.log('   ❌ 未启用自定义 tabBar');
      allChecks = false;
    }
    
    if (appConfig.tabBar.list && appConfig.tabBar.list.length === 3) {
      console.log('   ✅ tabBar 列表配置正确（3个标签）');
      
      // 检查页面路径
      const expectedPaths = [
        'pages/index/index',
        'pages/ai-chat/ai-chat',
        'pages/profile/profile'
      ];
      
      expectedPaths.forEach((expectedPath, index) => {
        if (appConfig.tabBar.list[index] && appConfig.tabBar.list[index].pagePath === expectedPath) {
          console.log(`   ✅ 标签 ${index} 路径正确: ${expectedPath}`);
        } else {
          console.log(`   ❌ 标签 ${index} 路径错误`);
          allChecks = false;
        }
      });
    } else {
      console.log('   ❌ tabBar 列表配置错误');
      allChecks = false;
    }
  } else {
    console.log('   ❌ 缺少 tabBar 配置');
    allChecks = false;
  }
  
  return allChecks;
}

/**
 * 检查页面 onShow 方法
 */
function checkPageOnShow(pagePath, pageName) {
  console.log(`\n📄 检查 ${pageName} 页面 onShow 方法...`);
  
  const content = readFile(pagePath);
  if (!content) return false;
  
  let allChecks = true;
  
  // 检查是否包含 onShow 方法
  if (content.includes('onShow()')) {
    console.log(`   ✅ ${pageName} 包含 onShow 方法`);
    
    // 检查是否调用了 setSelected 方法
    if (content.includes('this.getTabBar().setSelected()')) {
      console.log(`   ✅ ${pageName} 正确调用 setSelected 方法`);
    } else if (content.includes('this.getTabBar().setData')) {
      console.log(`   ⚠️  ${pageName} 使用旧的 setData 方法，建议更新`);
      allChecks = false;
    } else {
      console.log(`   ❌ ${pageName} 未调用 tabBar 更新方法`);
      allChecks = false;
    }
    
    // 检查错误处理
    if (content.includes('try') && content.includes('catch')) {
      console.log(`   ✅ ${pageName} 包含错误处理`);
    } else {
      console.log(`   ⚠️  ${pageName} 建议添加错误处理`);
    }
  } else {
    console.log(`   ❌ ${pageName} 缺少 onShow 方法`);
    allChecks = false;
  }
  
  return allChecks;
}

/**
 * 主测试函数
 */
function runTests() {
  console.log('🧪 开始测试 TabBar 修复...\n');
  
  let allTestsPassed = true;
  
  // 测试自定义 TabBar
  if (!checkCustomTabBar()) {
    allTestsPassed = false;
  }
  
  // 测试 app.json
  if (!checkAppJson()) {
    allTestsPassed = false;
  }
  
  // 测试页面 onShow 方法
  const pages = [
    { path: INDEX_PAGE_PATH, name: '首页' },
    { path: AI_CHAT_PAGE_PATH, name: 'AI聊天页' },
    { path: PROFILE_PAGE_PATH, name: '个人中心页' }
  ];
  
  pages.forEach(page => {
    if (!checkPageOnShow(page.path, page.name)) {
      allTestsPassed = false;
    }
  });
  
  // 输出测试结果
  console.log('\n' + '='.repeat(50));
  if (allTestsPassed) {
    console.log('🎉 所有测试通过！TabBar 修复成功！');
    console.log('\n✅ 修复内容:');
    console.log('   - 优化了自定义 TabBar 初始化逻辑');
    console.log('   - 添加了重试机制和错误处理');
    console.log('   - 改进了页面路径匹配算法');
    console.log('   - 更新了页面 onShow 方法调用');
    console.log('   - 增加了详细的调试日志');
    
    console.log('\n📋 下一步操作:');
    console.log('   1. 在微信开发者工具中重新编译项目');
    console.log('   2. 测试 TabBar 切换功能');
    console.log('   3. 检查控制台是否还有错误信息');
    console.log('   4. 验证页面切换时 TabBar 状态是否正确');
    
    console.log('\n🚀 "No pages found" 错误应该已经解决！');
  } else {
    console.log('❌ 部分测试失败，请检查上述错误信息');
    console.log('\n🔧 建议操作:');
    console.log('   1. 根据错误信息修复相关问题');
    console.log('   2. 重新运行测试脚本');
    console.log('   3. 确保所有文件都已正确保存');
  }
  
  return allTestsPassed;
}

// 运行测试
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
